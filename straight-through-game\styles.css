* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0A0A1A 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: #e8e8e8;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.game-header {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #4a9eff;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
}

.game-header h1 {
    color: #4a9eff;
    font-size: 2.5em;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.score-board {
    display: flex;
    gap: 30px;
    font-size: 1.2em;
    font-weight: bold;
    color: #e8e8e8;
}

.tutorial-panel {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #4a9eff;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
}

.tutorial-panel h2 {
    color: #4a9eff;
    margin-bottom: 15px;
    font-size: 1.8em;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.tutorial-panel p {
    margin-bottom: 20px;
    line-height: 1.6;
    font-size: 1.1em;
    color: #e8e8e8;
}

.wire-standard {
    background: rgba(16, 33, 62, 0.8);
    border: 1px solid #4a9eff;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
}

.wire-standard h3 {
    margin-bottom: 15px;
    color: #4a9eff;
}

.pin-layout {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 600px;
}

.pin {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    background: rgba(26, 26, 46, 0.9);
    border-radius: 5px;
    border: 2px solid #4a9eff;
}

.pin-number {
    font-weight: bold;
    width: 20px;
    text-align: center;
    color: #e8e8e8;
}

.wire-color {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 0.9em;
    font-weight: bold;
}

.orange-white { background: linear-gradient(45deg, #ff6b35 50%, white 50%); }
.orange { background: #ff6b35; color: white; }
.green-white { background: linear-gradient(45deg, #4caf50 50%, white 50%); }
.blue { background: #2196f3; color: white; }
.blue-white { background: linear-gradient(45deg, #2196f3 50%, white 50%); }
.green { background: #4caf50; color: white; }
.brown-white { background: linear-gradient(45deg, #8d6e63 50%, white 50%); }
.brown { background: #8d6e63; color: white; }

.start-btn, .check-btn, .reset-btn, .tutorial-btn, .mode-btn {
    background: linear-gradient(45deg, #4a9eff, #357abd);
    color: white;
    border: 2px solid #4a9eff;
    padding: 15px 30px;
    font-size: 1.1em;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.start-btn:hover, .check-btn:hover, .mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 158, 255, 0.4);
    background: linear-gradient(45deg, #357abd, #2a5f8f);
}

.reset-btn {
    background: linear-gradient(45deg, #ff6b6b, #e55555);
    border-color: #ff6b6b;
}

.reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    background: linear-gradient(45deg, #e55555, #cc4444);
}

.tutorial-btn {
    background: linear-gradient(45deg, #a855f7, #8b5cf6);
    border-color: #a855f7;
}

.tutorial-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(168, 85, 247, 0.4);
    background: linear-gradient(45deg, #8b5cf6, #7c3aed);
}

.game-area {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #4a9eff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(74, 158, 255, 0.2);
}

.cable-section {
    margin-bottom: 30px;
}

.cable-section h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #4a9eff;
    font-size: 1.5em;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.connector {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.rj45-connector {
    display: flex;
    background: #16213e;
    border: 2px solid #4a9eff;
    padding: 15px;
    border-radius: 10px;
    gap: 5px;
    box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
}

.pin-slot {
    width: 40px;
    height: 60px;
    background: #1a1a2e;
    border: 3px solid #4a9eff;
    border-radius: 5px;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.pin-slot.occupied {
    border-color: #00ff88;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
}

.pin-slot:hover {
    border-color: #66d9ff;
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(102, 217, 255, 0.4);
}

.pin-slot.correct {
    border-color: #00ff88;
    background: rgba(0, 255, 136, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

.pin-slot.incorrect {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.2);
    animation: shake 0.5s ease-in-out;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.pin-labels {
    display: flex;
    gap: 5px;
}

.pin-labels span {
    width: 40px;
    text-align: center;
    font-weight: bold;
    color: #e8e8e8;
}

.cable-middle {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.cable-visual {
    width: 300px;
    height: 20px;
    background: linear-gradient(90deg, #4a9eff, #357abd);
    border: 2px solid #66d9ff;
    border-radius: 10px;
    position: relative;
    box-shadow: 0 2px 10px rgba(74, 158, 255, 0.4);
}

.cable-body::before {
    content: 'CAT5e';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.8em;
}

.wire-palette {
    background: rgba(16, 33, 62, 0.8);
    border: 2px solid #4a9eff;
    padding: 20px;
    border-radius: 10px;
    margin: 30px 0;
}

.wire-palette h3 {
    margin-bottom: 15px;
    color: #4a9eff;
    text-align: center;
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.wires-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.wire {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(26, 26, 46, 0.9);
    border-radius: 8px;
    border: 2px solid #4a9eff;
    cursor: grab;
    transition: all 0.3s ease;
    color: #e8e8e8;
}

.wire:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 158, 255, 0.4);
    border-color: #66d9ff;
}

.wire:active {
    cursor: grabbing;
}

.wire.selected {
    border-color: #00ff88;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
    background: rgba(0, 255, 136, 0.1);
}

.wire.unavailable {
    opacity: 0.3;
    pointer-events: none;
}

.game-progress {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}

.current-side h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.current-side p {
    margin: 0;
    color: #6c757d;
    font-style: italic;
}

.next-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.next-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.wire-visual {
    width: 30px;
    height: 8px;
    border-radius: 4px;
    border: 1px solid #333;
}

.orange-white-wire { background: linear-gradient(90deg, #ff6b35 50%, white 50%); }
.orange-wire { background: #ff6b35; }
.green-white-wire { background: linear-gradient(90deg, #4caf50 50%, white 50%); }
.blue-wire { background: #2196f3; }
.blue-white-wire { background: linear-gradient(90deg, #2196f3 50%, white 50%); }
.green-wire { background: #4caf50; }
.brown-white-wire { background: linear-gradient(90deg, #8d6e63 50%, white 50%); }
.brown-wire { background: #8d6e63; }

.interaction-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: rgba(16, 33, 62, 0.6);
    border: 2px solid #4a9eff;
    border-radius: 10px;
}

.mode-indicator {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

.mode-indicator.drag-mode {
    background: rgba(74, 158, 255, 0.2);
    color: #4a9eff;
    border: 2px solid #4a9eff;
}

.mode-indicator.click-mode {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 2px solid #00ff88;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
}

.feedback {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
    font-size: 1.1em;
    font-weight: bold;
}

.feedback.success {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: 2px solid #00ff88;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.feedback.error {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 2px solid #ff6b6b;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.feedback.info {
    background: rgba(74, 158, 255, 0.2);
    color: #4a9eff;
    border: 2px solid #4a9eff;
    box-shadow: 0 0 15px rgba(74, 158, 255, 0.3);
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    .pin-layout {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wires-container {
        grid-template-columns: 1fr;
    }
    
    .game-controls {
        flex-direction: column;
        align-items: center;
    }

    .interaction-controls {
        padding: 15px;
    }

    .mode-indicator {
        font-size: 0.9em;
        padding: 8px 15px;
    }
}
