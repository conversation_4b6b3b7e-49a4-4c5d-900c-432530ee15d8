class StraightThroughCableGame {
    constructor() {
        this.score = 0;
        this.attempts = 0;
        this.connectorA = {};
        this.connectorB = {};
        this.selectedWire = null; // For click-and-drop functionality
        this.clickMode = false; // Toggle between drag-drop and click-drop

        // T568B standard for straight-through cable (same on both ends)
        this.correctWiring = {
            1: 'orange-white',
            2: 'orange',
            3: 'green-white',
            4: 'blue',
            5: 'blue-white',
            6: 'green',
            7: 'brown-white',
            8: 'brown'
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupDragAndDrop();
        this.setupClickAndDrop();
    }

    bindEvents() {
        document.getElementById('startGame').addEventListener('click', () => this.startGame());
        document.getElementById('checkWiring').addEventListener('click', () => this.checkWiring());
        document.getElementById('resetGame').addEventListener('click', () => this.resetGame());
        document.getElementById('showTutorial').addEventListener('click', () => this.showTutorial());
        document.getElementById('toggleMode').addEventListener('click', () => this.toggleInteractionMode());
    }
    
    startGame() {
        document.getElementById('tutorialPanel').style.display = 'none';
        document.getElementById('gameArea').style.display = 'block';
        this.resetGame();
    }
    
    showTutorial() {
        document.getElementById('tutorialPanel').style.display = 'block';
        document.getElementById('gameArea').style.display = 'none';
    }
    
    setupDragAndDrop() {
        const wires = document.querySelectorAll('.wire');
        const pinSlots = document.querySelectorAll('.pin-slot');

        wires.forEach(wire => {
            wire.addEventListener('dragstart', (e) => {
                if (this.clickMode) return; // Disable drag in click mode
                e.dataTransfer.setData('text/plain', wire.dataset.color);
                wire.style.opacity = '0.5';
            });

            wire.addEventListener('dragend', (e) => {
                if (this.clickMode) return;
                wire.style.opacity = '1';
            });
        });

        pinSlots.forEach(slot => {
            slot.addEventListener('dragover', (e) => {
                if (this.clickMode) return; // Disable drag in click mode
                e.preventDefault();
                // Show preview of wire color being dragged
                const wireColor = e.dataTransfer.getData('text/plain');
                if (wireColor) {
                    slot.style.background = this.getWireColor(wireColor);
                    slot.style.opacity = '0.7';
                } else {
                    slot.style.backgroundColor = '#e8f5e8';
                }
            });

            slot.addEventListener('dragleave', (e) => {
                if (this.clickMode) return;
                if (!slot.classList.contains('occupied')) {
                    slot.style.background = '';
                    slot.style.backgroundColor = '';
                    slot.style.opacity = '1';
                }
            });

            slot.addEventListener('drop', (e) => {
                if (this.clickMode) return; // Disable drag in click mode
                e.preventDefault();
                const wireColor = e.dataTransfer.getData('text/plain');
                const pin = slot.dataset.pin;
                const connector = slot.dataset.connector;

                this.placeWire(connector, pin, wireColor, slot);
                slot.style.opacity = '1';
            });

            // Click to remove wire (works in both modes)
            slot.addEventListener('click', (e) => {
                if (slot.classList.contains('occupied') && !this.selectedWire) {
                    this.removeWire(slot);
                }
            });
        });
    }

    setupClickAndDrop() {
        const wires = document.querySelectorAll('.wire');
        const pinSlots = document.querySelectorAll('.pin-slot');

        // Add click handlers for wires
        wires.forEach(wire => {
            wire.addEventListener('click', (e) => {
                if (!this.clickMode) return; // Only work in click mode

                // Clear previous selection
                this.clearWireSelection();

                // Select this wire
                this.selectedWire = wire.dataset.color;
                wire.classList.add('selected');

                // Update visual feedback
                this.updateModeIndicator();
            });
        });

        // Add click handlers for pin slots (for placing wires)
        pinSlots.forEach(slot => {
            slot.addEventListener('click', (e) => {
                if (!this.clickMode) return; // Only work in click mode

                if (this.selectedWire && !slot.classList.contains('occupied')) {
                    // Place the selected wire
                    const pin = slot.dataset.pin;
                    const connector = slot.dataset.connector;
                    this.placeWire(connector, pin, this.selectedWire, slot);

                    // Clear selection after placing
                    this.clearWireSelection();
                }
            });
        });
    }

    clearWireSelection() {
        // Remove selection from all wires
        document.querySelectorAll('.wire').forEach(wire => {
            wire.classList.remove('selected');
        });
        this.selectedWire = null;
        this.updateModeIndicator();
    }

    toggleInteractionMode() {
        this.clickMode = !this.clickMode;
        this.clearWireSelection();

        // Update wire draggable attribute
        const wires = document.querySelectorAll('.wire');
        wires.forEach(wire => {
            wire.draggable = !this.clickMode;
        });

        this.updateModeIndicator();
    }

    updateModeIndicator() {
        const modeBtn = document.getElementById('toggleMode');
        const modeIndicator = document.getElementById('modeIndicator');

        if (this.clickMode) {
            modeBtn.textContent = 'Switch to Drag & Drop';
            modeIndicator.textContent = this.selectedWire ?
                `Click Mode - Selected: ${this.getWireDisplayName(this.selectedWire)}` :
                'Click Mode - Select a wire, then click a pin slot';
            modeIndicator.className = 'mode-indicator click-mode';
        } else {
            modeBtn.textContent = 'Switch to Click & Drop';
            modeIndicator.textContent = 'Drag & Drop Mode - Drag wires to pin slots';
            modeIndicator.className = 'mode-indicator drag-mode';
        }
    }

    placeWire(connector, pin, wireColor, slot) {
        // Remove existing wire if present
        if (slot.classList.contains('occupied')) {
            this.removeWire(slot);
        }

        // Store the wire placement
        if (connector === 'A') {
            this.connectorA[pin] = wireColor;
        } else {
            this.connectorB[pin] = wireColor;
        }

        // Visual feedback - show the actual wire color
        slot.classList.add('occupied');
        slot.style.background = this.getWireColor(wireColor);
        slot.style.border = '3px solid #27ae60';
        slot.setAttribute('data-wire-color', wireColor);

        // Add wire label
        const wireLabel = document.createElement('div');
        wireLabel.className = 'wire-label';
        wireLabel.textContent = this.getWireDisplayName(wireColor);
        wireLabel.style.cssText = `
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7em;
            font-weight: bold;
            color: #fff;
            background: rgba(0, 0, 0, 0.7);
            padding: 1px 3px;
            border-radius: 2px;
            text-shadow: none;
        `;
        slot.appendChild(wireLabel);

        this.playSound('place');
    }
    
    removeWire(slot) {
        const pin = slot.dataset.pin;
        const connector = slot.dataset.connector;

        // Remove from data
        if (connector === 'A') {
            delete this.connectorA[pin];
        } else {
            delete this.connectorB[pin];
        }

        // Remove visual elements
        slot.classList.remove('occupied', 'correct', 'incorrect');
        slot.style.background = '';
        slot.style.backgroundColor = '';
        slot.style.border = '3px solid #bdc3c7';
        slot.removeAttribute('data-wire-color');

        const wireLabel = slot.querySelector('.wire-label');
        if (wireLabel) {
            wireLabel.remove();
        }

        this.playSound('remove');
    }
    
    getWireColor(wireColor) {
        const colors = {
            'orange-white': 'linear-gradient(45deg, #ff6b35 50%, white 50%)',
            'orange': '#ff6b35',
            'green-white': 'linear-gradient(45deg, #4caf50 50%, white 50%)',
            'blue': '#2196f3',
            'blue-white': 'linear-gradient(45deg, #2196f3 50%, white 50%)',
            'green': '#4caf50',
            'brown-white': 'linear-gradient(45deg, #8d6e63 50%, white 50%)',
            'brown': '#8d6e63'
        };
        return colors[wireColor] || '#ecf0f1';
    }
    
    getWireDisplayName(wireColor) {
        const names = {
            'orange-white': 'O/W',
            'orange': 'O',
            'green-white': 'G/W',
            'blue': 'B',
            'blue-white': 'B/W',
            'green': 'G',
            'brown-white': 'Br/W',
            'brown': 'Br'
        };
        return names[wireColor] || '';
    }
    
    checkWiring() {
        this.attempts++;
        document.getElementById('attempts').textContent = this.attempts;
        
        let correctConnections = 0;
        let totalConnections = 0;
        let errors = [];
        
        // Check if both connectors are fully wired
        const requiredPins = [1, 2, 3, 4, 5, 6, 7, 8];
        
        for (let pin of requiredPins) {
            const pinStr = pin.toString();
            const slotA = document.querySelector(`[data-pin="${pinStr}"][data-connector="A"]`);
            const slotB = document.querySelector(`[data-pin="${pinStr}"][data-connector="B"]`);
            
            totalConnections += 2; // Count both ends
            
            // Check connector A
            if (this.connectorA[pinStr] === this.correctWiring[pin]) {
                correctConnections++;
                slotA.classList.add('correct');
                slotA.classList.remove('incorrect');
            } else {
                slotA.classList.add('incorrect');
                slotA.classList.remove('correct');
                if (this.connectorA[pinStr]) {
                    errors.push(`Pin ${pin} End A: Expected ${this.getWireDisplayName(this.correctWiring[pin])}, got ${this.getWireDisplayName(this.connectorA[pinStr])}`);
                } else {
                    errors.push(`Pin ${pin} End A: Missing wire`);
                }
            }
            
            // Check connector B
            if (this.connectorB[pinStr] === this.correctWiring[pin]) {
                correctConnections++;
                slotB.classList.add('correct');
                slotB.classList.remove('incorrect');
            } else {
                slotB.classList.add('incorrect');
                slotB.classList.remove('correct');
                if (this.connectorB[pinStr]) {
                    errors.push(`Pin ${pin} End B: Expected ${this.getWireDisplayName(this.correctWiring[pin])}, got ${this.getWireDisplayName(this.connectorB[pinStr])}`);
                } else {
                    errors.push(`Pin ${pin} End B: Missing wire`);
                }
            }
        }
        
        this.showFeedback(correctConnections, totalConnections, errors);
        
        if (correctConnections === totalConnections) {
            this.score += Math.max(100 - (this.attempts - 1) * 10, 10);
            document.getElementById('score').textContent = this.score;
            this.playSound('success');
        } else {
            this.playSound('error');
        }
    }
    
    showFeedback(correct, total, errors) {
        const feedback = document.getElementById('feedback');
        
        if (correct === total) {
            feedback.className = 'feedback success';
            feedback.innerHTML = `
                <h3>🎉 Perfect! Cable terminated correctly!</h3>
                <p>You've successfully created a straight-through cable using T568B standard on both ends.</p>
                <p>Score earned: ${Math.max(100 - (this.attempts - 1) * 10, 10)} points</p>
            `;
        } else {
            feedback.className = 'feedback error';
            feedback.innerHTML = `
                <h3>❌ Incorrect wiring detected</h3>
                <p>Correct connections: ${correct}/${total}</p>
                <div style="text-align: left; margin-top: 10px;">
                    <strong>Errors:</strong>
                    <ul style="margin-left: 20px;">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
                <p style="margin-top: 10px;"><em>Remember: Straight-through cables use T568B on both ends!</em></p>
            `;
        }
    }
    
    resetGame() {
        this.connectorA = {};
        this.connectorB = {};

        // Clear wire selection
        this.clearWireSelection();

        // Clear all pin slots
        const pinSlots = document.querySelectorAll('.pin-slot');
        pinSlots.forEach(slot => {
            this.removeWire(slot);
        });

        // Clear feedback
        const feedback = document.getElementById('feedback');
        feedback.className = 'feedback';
        feedback.innerHTML = '';

        this.playSound('reset');
    }
    
    playSound(type) {
        // Create audio context for sound effects
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        switch (type) {
            case 'place':
                oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
                break;
            case 'remove':
                oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
                break;
            case 'success':
                oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
                break;
            case 'error':
                oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
                break;
            case 'reset':
                oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime);
                break;
        }
        
        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new StraightThroughCableGame();
    // Initialize mode indicator
    game.updateModeIndicator();
});
